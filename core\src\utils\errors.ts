// Error type definitions and utilities

export enum ErrorCode {
  // Configuration errors
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  
  // Authentication errors
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  
  // API errors
  API_ERROR = 'API_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  QUOTA_EXCEEDED_ERROR = 'QUOTA_EXCEEDED_ERROR',
  
  // Model errors
  MODEL_ERROR = 'MODEL_ERROR',
  PROVIDER_ERROR = 'PROVIDER_ERROR',
  
  // Tool errors
  TOOL_ERROR = 'TOOL_ERROR',
  TOOL_VALIDATION_ERROR = 'TOOL_VALIDATION_ERROR',
  TOOL_EXECUTION_ERROR = 'TOOL_EXECUTION_ERROR',
  
  // File system errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_ERROR = 'FILE_ACCESS_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  
  // Security errors
  SECURITY_ERROR = 'SECURITY_ERROR',
  SANDBOX_ERROR = 'SANDBOX_ERROR',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // User errors
  USER_CANCELLED = 'USER_CANCELLED',
  INVALID_INPUT = 'INVALID_INPUT',
  
  // System errors
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  
  // Unknown errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class ArienError extends Error {
  public readonly code: ErrorCode;
  public readonly details?: any;
  public readonly timestamp: Date;

  constructor(code: ErrorCode, message: string, details?: any) {
    super(message);
    this.name = 'ArienError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date();
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ArienError);
    }
  }

  public toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack
    };
  }
}

export class ConfigurationError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.CONFIGURATION_ERROR, message, details);
    this.name = 'ConfigurationError';
  }
}

export class AuthenticationError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.AUTHENTICATION_ERROR, message, details);
    this.name = 'AuthenticationError';
  }
}

export class ToolError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.TOOL_ERROR, message, details);
    this.name = 'ToolError';
  }
}

export class SecurityError extends ArienError {
  constructor(message: string, details?: any) {
    super(ErrorCode.SECURITY_ERROR, message, details);
    this.name = 'SecurityError';
  }
}

export class FileSystemError extends ArienError {
  constructor(code: ErrorCode, message: string, details?: any) {
    super(code, message, details);
    this.name = 'FileSystemError';
  }
}

export function isArienError(error: any): error is ArienError {
  return error instanceof ArienError;
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof ArienError) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

export function getErrorCode(error: unknown): ErrorCode {
  if (error instanceof ArienError) {
    return error.code;
  }
  return ErrorCode.UNKNOWN_ERROR;
}

export function createErrorFromCode(code: ErrorCode, message: string, details?: any): ArienError {
  switch (code) {
    case ErrorCode.CONFIGURATION_ERROR:
      return new ConfigurationError(message, details);
    case ErrorCode.AUTHENTICATION_ERROR:
      return new AuthenticationError(message, details);
    case ErrorCode.TOOL_ERROR:
    case ErrorCode.TOOL_VALIDATION_ERROR:
    case ErrorCode.TOOL_EXECUTION_ERROR:
      return new ToolError(message, details);
    case ErrorCode.SECURITY_ERROR:
    case ErrorCode.SANDBOX_ERROR:
      return new SecurityError(message, details);
    case ErrorCode.FILE_NOT_FOUND:
    case ErrorCode.FILE_ACCESS_ERROR:
    case ErrorCode.PERMISSION_DENIED:
      return new FileSystemError(code, message, details);
    default:
      return new ArienError(code, message, details);
  }
}

export interface ErrorRecoveryStrategy {
  canRecover: (error: ArienError) => boolean;
  recover: (error: ArienError) => Promise<void>;
  maxRetries?: number;
}

export class ErrorRecoveryManager {
  private strategies: ErrorRecoveryStrategy[] = [];

  public addStrategy(strategy: ErrorRecoveryStrategy): void {
    this.strategies.push(strategy);
  }

  public async tryRecover(error: ArienError): Promise<boolean> {
    for (const strategy of this.strategies) {
      if (strategy.canRecover(error)) {
        try {
          await strategy.recover(error);
          return true;
        } catch (recoveryError) {
          console.warn('Recovery strategy failed:', recoveryError);
        }
      }
    }
    return false;
  }
}

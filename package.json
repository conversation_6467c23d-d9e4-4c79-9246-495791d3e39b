{"name": "arien-cli", "version": "1.0.0", "description": "A powerful and modern CLI Terminal system powered by AI with support for multiple providers", "main": "index.js", "type": "module", "scripts": {"build": "npm run build:core && npm run build:cli", "build:core": "cd core && npm run build", "build:cli": "cd cli && npm run build", "dev": "cd cli && npm run dev", "start": "cd cli && npm start", "test": "npm run test:core && npm run test:cli", "test:core": "cd core && npm test", "test:cli": "cd cli && npm test", "lint": "npm run lint:core && npm run lint:cli", "lint:core": "cd core && npm run lint", "lint:cli": "cd cli && npm run lint", "install:all": "npm install && cd core && npm install && cd ../cli && npm install", "clean": "rm -rf node_modules core/node_modules cli/node_modules core/dist cli/dist"}, "keywords": ["cli", "ai", "terminal", "deepseek", "openai", "anthropic", "google", "assistant"], "author": "Arien CLI Team", "license": "MIT", "workspaces": ["cli", "core"], "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}, "engines": {"node": ">=18.0.0"}}